import React from 'react';
import { Form, Formik, useFormikContext } from 'formik';
import { Button, Typography } from '@mui/material';
import AutoConfigDropdown from '../AutoConfigDropdown';
import { useOutletContext } from 'react-router-dom';

const GST_LEDGERS = [0, 5, 12, 18, 28];

export default function Texes() {
  const { isZoho, businessPreferences } = useOutletContext();
  const gstLedgerMode = businessPreferences?.gst_ledger_mode;
  const handleSubmit = (values) => {
    console.log(values);
  };
  const initialValues = {
    gst_ledger_mode: gstLedgerMode?.value ?? '',
    cess_ledger: '',
  };
  return (
    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
      {({ values, setFieldValue, dirty }) => (
        <Form className="flex flex-col gap-2 px-5 max-w-4xl ml-10">
          <Typography variant="h6" fontWeight="bold" className="mb-5">
            Tax Configuration
          </Typography>

          <AutoConfigDropdown
            label="GST Ledger Mode (for Purchases)"
            onSelect={(value) => setFieldValue('gst_ledger_mode', value?.value ?? '')}
            value={values.gst_ledger_mode}
            optionLabel="value"
            optionValue="value"
            config={gstLedgerMode}
          />
          {!isZoho && (
            <AutoConfigDropdown
              label="Cess Ledger"
              onSelect={(value) => setFieldValue('cess_ledger', value?.uuid_id ?? '')}
              value={values.cess_ledger}
            />
          )}

          <div className={`grid gap-2 ${isZoho ? 'grid-cols-2' : 'grid-cols-3'}`}>
            {isZoho ? <ZohoTaxes /> : <TallyTaxes />}
          </div>

          <Button variant="contained" className="w-fit" type="submit" disabled={!dirty}>
            Save
          </Button>
        </Form>
      )}
    </Formik>
  );
}

function TallyTaxes() {
  const { values, setFieldValue } = useFormikContext();

  const isConsolidated = values.gst_ledger_mode === 'consolidated';

  const handleConsolidatedChange = (gstType, value, gstRate) => {
    if (isConsolidated && gstRate === 0) {
      // When in consolidated mode and 0% rate is changed, update all other rates
      GST_LEDGERS.forEach((rate) => {
        setFieldValue(`${gstType}_ledger_${rate}`, value?.uuid_id ?? '');
      });
    } else {
      // Normal field update
      setFieldValue(`${gstType}_ledger_${gstRate}`, value?.uuid_id ?? '');
    }
  };

  return (
    <>
      {/* IGST */}
      <div className="flex flex-col">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          IGST
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            key={`igst_${gst}`}
            label={`${gst}%`}
            onSelect={(value) => handleConsolidatedChange('igst', value, gst)}
            value={values[`igst_ledger_${gst}`]}
            disabled={isConsolidated && gst !== 0}
          />
        ))}
      </div>

      {/* CGST */}
      <div className="flex flex-col">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          CGST
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            key={`cgst_${gst}`}
            label={`${gst}%`}
            onSelect={(value) => handleConsolidatedChange('cgst', value, gst)}
            value={values[`cgst_ledger_${gst}`]}
            disabled={isConsolidated && gst !== 0}
          />
        ))}
      </div>

      <div className="flex flex-col">
        {/* SGST */}
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          SGST
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            key={`sgst_${gst}`}
            label={`${gst}%`}
            onSelect={(value) => handleConsolidatedChange('sgst', value, gst)}
            value={values[`sgst_ledger_${gst}`]}
            disabled={isConsolidated && gst !== 0}
          />
        ))}
      </div>
    </>
  );
}

function ZohoTaxes() {
  const { values, setFieldValue } = useFormikContext();
  return (
    <>
      {/* Inter */}
      <div className="flex flex-col gap-1">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          Inter State
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            label={`${gst}%`}
            onSelect={(value) => setFieldValue(`inter_state_ledger_${gst}`, value?.uuid_id ?? '')}
            value={values[`inter_state_ledger_${gst}`]}
          />
        ))}
      </div>

      {/* Intra */}
      <div className="flex flex-col gap-1">
        <Typography variant="subtitle1" fontWeight="bold" className="mb-2">
          Intra State
        </Typography>
        {GST_LEDGERS.map((gst) => (
          <AutoConfigDropdown
            label={`${gst}%`}
            onSelect={(value) => setFieldValue(`intra_state_ledger_${gst}`, value?.uuid_id ?? '')}
            value={values[`intra_state_ledger_${gst}`]}
          />
        ))}
      </div>
    </>
  );
}
