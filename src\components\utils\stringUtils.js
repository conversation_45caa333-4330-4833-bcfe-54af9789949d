const sliceText = (text, sliceTill = 20) => {
  if (!text) return '';
  return text.length > sliceTill ? text.slice(0, sliceTill) + '...' : text;
};

const parseStringToValue = (val) => {
  if (typeof val !== 'string') return val;
  if (!isNaN(val) && val.trim() !== '') {
    return val.includes('.') ? parseFloat(val) : parseInt(val, 10);
  }
  if (val.toLowerCase() === 'true') return true;
  if (val.toLowerCase() === 'false') return false;
  return val;
};

export { sliceText, parseStringToValue };
