import React from 'react';
import AutoConfigDropdown from '../AutoConfigDropdown';
import { Form, Formik } from 'formik';
import { Button } from '@mui/material';
import LedgerTree from '../LedgerTree';
import DownloadBtn from '../../../custom-components/DownloadBtn';
import UploadBtn from '../../../custom-components/UploadBtn';
import { autoConfigUrls } from '../../../utils/apiurls';
import { useAuth } from '../../../../contexts/AuthContext';
import { useOutletContext } from 'react-router-dom';
import Checkbox from '../../../ui-components/fields/Checkbox';

const data = [
  {
    parent_group_name: 'Accounts Payable',
    ledgers: [
      {
        uuid_id: 'e1abced7-4a3c-519b-b7c4-fc11e2322146',
        ledger_name: 'me',
      },
    ],
  },
  {
    parent_group_name: 'Prepaid Expenses',
    ledgers: [
      {
        parent_ledger_name: 'Test Prepaid',
        ledgers: [
          {
            parent_ledger_name: null,
            ledgers: [
              {
                parent_ledger_name: 'test sub 2',
                ledgers: [
                  {
                    ledger_name: 'test sub 3',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    parent_group_name: 'Prepaid Expenses',
    ledgers: [
      {
        parent_ledger_name: 'Test Prepaid',
        ledgers: [
          {
            parent_ledger_name: null,
            ledgers: [
              {
                parent_ledger_name: 'test sub 2',
                ledgers: [
                  {
                    parent_ledger_name: 'test sub 3',
                    ledgers: [
                      {
                        parent_ledger_name: 'test sub 4',
                        ledgers: [
                          {
                            ledger_name: 'test sub 5',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    parent_group_name: 'Prepaid Expenses',
    ledgers: [
      {
        parent_ledger_name: 'Test Prepaid',
        ledgers: [
          {
            parent_ledger_name: null,
            ledgers: [
              {
                parent_ledger_name: 'test sub 2',
                ledgers: [
                  {
                    parent_ledger_name: 'test sub 3',
                    ledgers: [
                      {
                        parent_ledger_name: 'test sub 4',
                        ledgers: [
                          {
                            ledger_name: 'test sub 5',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
];

function Ledgers() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences } = useOutletContext();

  const manageInventory = businessPreferences?.is_inventory_enabled;

  const handleSubmit = (values) => {
    console.log('values ::: ', values);
  };

  const initialValues = {
    purchase: '',
    tcs: '',
    round_off: '',
    discount: '',
    freight: '',
    insurance: '',
    other_charges: '',
    default_invoice_type: businessPreferences?.default_voucher?.value ?? '',
    manage_inventory: manageInventory?.value ?? false,
  };

  return (
    <div className="flex gap-4 mx-4 items-start h-full">
      {/* Default Ledger */}
      <div className="w-1/2 p-2 bg-white rounded-lg border border-gray-bg shadow-lg">
        <h3 className="px-4 py-2 border-b border-gray-300 text-lg font-semibold text-primary-color flex items-center gap-2 h-12">
          Default Ledger
        </h3>
        <Formik initialValues={initialValues} onSubmit={handleSubmit}>
          {({ values, setFieldValue, dirty }) => (
            <Form className="grid grid-cols-2 gap-4 p-2.5">
              <AutoConfigDropdown
                label="Purchase"
                onSelect={(value) => setFieldValue('purchase', value?.uuid_id ?? '')}
                value={values.purchase}
              />

              <AutoConfigDropdown
                label="TCS"
                onSelect={(value) => setFieldValue('tcs', value?.uuid_id ?? '')}
                value={values.tcs}
              />

              <AutoConfigDropdown
                label="Round off"
                onSelect={(value) => setFieldValue('round_off', value?.uuid_id ?? '')}
                value={values.round_off}
              />

              <AutoConfigDropdown
                label="Discount"
                onSelect={(value) => setFieldValue('discount', value?.uuid_id ?? '')}
                value={values.discount}
              />

              <AutoConfigDropdown
                label="Freight"
                onSelect={(value) => setFieldValue('freight', value?.uuid_id ?? '')}
                value={values.freight}
              />

              <AutoConfigDropdown
                label="Insurance"
                onSelect={(value) => setFieldValue('insurance', value?.uuid_id ?? '')}
                value={values.insurance}
              />

              <AutoConfigDropdown
                label="Other Charges"
                onSelect={(value) => setFieldValue('other_charges', value?.uuid_id ?? '')}
                value={values.other_charges}
              />

              <AutoConfigDropdown
                label="Default Invoice Type"
                onSelect={(value) => setFieldValue('default_invoice_type', value?.value ?? '')}
                value={values.default_invoice_type}
                optionLabel="value"
                optionValue="value"
                config={businessPreferences?.default_voucher}
              />

              <div className="flex items-center justify-between gap-2">
                {manageInventory?.show_field && (
                  <Checkbox
                    label="Manage Inventory"
                    checked={values.manage_inventory}
                    onChange={(checked) => setFieldValue('manage_inventory', checked)}
                    className="m-2"
                    disabled={manageInventory?.access !== 'RW'}
                  />
                )}

                <Button variant="contained" color="primary" type="submit" disabled={!dirty}>
                  Save
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>

      {/* Ledger Hierarchy */}
      <div className="w-1/2 p-2 bg-white rounded-lg border border-gray-bg shadow-lg flex flex-col h-full">
        <h3 className="px-4 py-2 border-b border-gray-300 text-lg font-semibold text-primary-color flex items-center justify-between h-12 flex-shrink-0">
          <span>Ledger Hierarchy</span>
          {!businessPreferences?.enable_auto_sync_master?.value && (
            <div className="flex gap-2 flex-shrink-0">
              <DownloadBtn
                selectionOptions={[
                  {
                    label: 'Ledgers',
                    value: 'ledgers',
                    url: `${autoConfigUrls.downloadLedgers}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                  {
                    label: 'Ledger Groups',
                    value: 'ledger_groups',
                    url: `${autoConfigUrls.downloadLedgerGroups}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                ]}
              />
              <UploadBtn
                selectionOptions={[
                  {
                    label: 'Ledgers',
                    value: 'ledgers',
                    url: `${autoConfigUrls.uploadLedgers}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                  {
                    label: 'Ledger Groups',
                    value: 'ledger_groups',
                    url: `${autoConfigUrls.uploadLedgerGroups}?business_id=${globSelectedBusiness?.business_id}`,
                  },
                ]}
              />
            </div>
          )}
        </h3>

        <div className="flex-1 overflow-y-auto min-h-0">
          <LedgerTree data={data} />
        </div>
      </div>
    </div>
  );
}

export default Ledgers;
