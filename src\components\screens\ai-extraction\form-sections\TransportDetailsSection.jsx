import React from 'react';
import <PERSON><PERSON>ield from './components/AiField';
import { getFieldAlertObject } from '../../../../components/utils/aiUtils';

const SECTION = 'transport_details';

function TransportDetailsSection({ formData, isReadOnly, formAction }) {
  const data = formData[SECTION] || {};

  return (
    <div className="form-grid">
      {/* Transport Name */}
      <AiField
        label="Transport Name"
        isExactMatch={data?.exact_match?.transport_name}
        alertObject={getFieldAlertObject(data, 'transport_name')}
        className="only-1-column"
        type="text"
        name="transport_name"
        id="transport_name"
        value={data?.transport_name ?? ''}
        onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'transport_name', e.target.value)}
        disabled={isReadOnly}
      />

      {/* Vehicle Details */}
      <AiField
        label="Vehicle Details"
        isExactMatch={data?.exact_match?.vehicle_details}
        alertObject={getFieldAlertObject(data, 'vehicle_details')}
        type="text"
        name="vehicle_details"
        id="vehicle_details"
        value={data?.vehicle_details ?? ''}
        onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'vehicle_details', e.target.value)}
        disabled={isReadOnly}
      />

      {/* Lorry Receipt Details */}
      <AiField
        label="Lorry Receipt Details"
        isExactMatch={data?.exact_match?.lr_details}
        alertObject={getFieldAlertObject(data, 'lr_details')}
        type="text"
        name="lr_details"
        id="lr_details"
        value={data?.lr_details ?? ''}
        onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'lr_details', e.target.value)}
        disabled={isReadOnly}
      />
    </div>
  );
}

export default TransportDetailsSection;
