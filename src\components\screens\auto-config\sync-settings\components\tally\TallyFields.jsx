import React, { useState } from 'react';
import { Typo<PERSON>, Switch, Button, IconButton } from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import { useQuery } from '@tanstack/react-query';
import { getTallyAPIKey } from '../../../../../services/tallyServices';
import { useAuth } from '../../../../../../contexts/AuthContext';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import DynamicField from '../../../../../custom-components/DynamicField';
import { useOutletContext } from 'react-router-dom';

function TallyFields() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences } = useOutletContext();
  const [autoSyncMaster, setAutoSyncMaster] = useState(businessPreferences?.enable_auto_sync_master?.value);
  const [autoSyncInvoice, setAutoSyncInvoice] = useState(businessPreferences?.enable_auto_sync_invoice?.value);
  const [invoiceLevelPurchaseLedger, setInvoiceLevelPurchaseLedger] = useState(false);
  const [copied, setCopied] = useState(false);
  const { data: tallyAPIKey } = useQuery({
    queryKey: ['tallyAPIKey'],
    queryFn: () => getTallyAPIKey(globSelectedBusiness?.business_id),
  });

  const handleAutoSyncMasterChange = (event) => {
    const checked = event.target.checked;
    setAutoSyncMaster(checked);
    if (!checked) {
      setAutoSyncInvoice(false);
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(tallyAPIKey?.api_key?.value ?? '');
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  const handleGenerateNewKey = () => {};

  const handleDownloadTdl = () => {
    const a = document.createElement('a');
    a.href = 'https://prod-atom-storage.s3.ap-south-1.amazonaws.com/tally+tdl/Omnisage+Tally+Integration.zip';
    a.download = 'OmniSageAI-Tally-Integration.zip';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <div className="flex justify-evenly gap-6">
      <div className="flex justify-between min-w-[60%] gap-16 p-5 rounded-2xl shadow-lg bg-white border border-accent1-border">
        <div className="flex flex-col gap-4">
          <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
            Synchronization Settings
          </Typography>
          <div className="flex flex-col gap-4">
            <DynamicField
              config={businessPreferences?.enable_auto_sync_master}
              control={<Switch checked={autoSyncMaster} onChange={handleAutoSyncMasterChange} color="primary" />}
              label="Auto Sync Master"
            />

            <DynamicField
              control={
                <Switch
                  checked={autoSyncInvoice}
                  onChange={(event) => setAutoSyncInvoice(event.target.checked)}
                  color="primary"
                />
              }
              config={businessPreferences?.enable_auto_sync_invoice}
              label="Auto Sync Invoice"
              disabled={!autoSyncMaster}
            />

            <DynamicField
              control={
                <Switch
                  checked={invoiceLevelPurchaseLedger}
                  onChange={(event) => setInvoiceLevelPurchaseLedger(event.target.checked)}
                  color="primary"
                />
              }
              config={businessPreferences?.purchase_ledger_mode}
              label="Invoice level purchase ledger"
            />
          </div>
        </div>

        <div className="flex flex-col gap-4 flex-1">
          <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
            API Configuration
          </Typography>
          <div className="flex flex-col">
            <p className="text-gray-600 text-base m-0">API Key</p>
            <div className="flex items-center gap-2 rounded-md p-2">
              <Typography variant="body1" className="h-10 flex-grow p-2 border rounded-md font-mono text-gray-600">
                {tallyAPIKey?.api_key?.value ?? ''}
              </Typography>
              <IconButton disabled={!tallyAPIKey?.api_key?.value} onClick={handleCopy}>
                {copied ? <CheckIcon color="success" /> : <ContentCopyIcon />}
              </IconButton>
            </div>
          </div>
          <Button variant="contained" className="!bg-accent2" onClick={handleGenerateNewKey}>
            Generate New Key
          </Button>
        </div>
      </div>

      <div className="flex flex-col justify-between gap-2 p-5 rounded-2xl shadow-lg bg-white border border-accent1-border">
        <Typography variant="h6" className="font-semibold mb-2 text-gray-800 border-b pb-2">
          Tally TDL and Instructions
        </Typography>

        <div className="flex flex-col justify-between gap-2">
          <p className="text-gray-600 text-lg">
            Download the Tally Definition Language (TDL) file and follow the included instructions to set up Tally
            integration with our system. If you need assistance with the setup process, please contact our support team.
          </p>

          <Button
            id="export-data-btn"
            onClick={handleDownloadTdl}
            variant="contained"
            startIcon={<FileDownloadOutlinedIcon size={16} />}
          >
            Download
          </Button>
        </div>
      </div>
    </div>
  );
}

export default TallyFields;
