import React, { useState } from 'react';
import DataTable from '../../../custom-components/DataTable';
import { useAuth } from '../../../../contexts/AuthContext';
import DownloadBtn from '../../../custom-components/DownloadBtn';
import UploadBtn from '../../../custom-components/UploadBtn';
import { useOutletContext } from 'react-router-dom';
import { Button } from '@mui/material';
import AddItemForm from '../../ai-extraction/form-sections/components/AddItemForm';
import { autoConfigUrls } from '../../../utils/apiurls';

const COLUMNS = [
  {
    field: 'name',
    headerName: 'Item Name',
  },
  {
    field: 'aliases',
    headerName: 'Aliases',
  },
  {
    field: 'hsn_sac',
    headerName: 'HSN/SAC Code',
  },
  {
    field: 'purchase_rate',
    headerName: 'Purchase Rate',
  },
  {
    field: 'unit',
    headerName: 'Unit',
  },
  {
    field: 'gst_rate',
    headerName: 'GST Rate',
  },
];

function Item() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences, isZoho } = useOutletContext();
  const [open, setOpen] = useState(false);

  const isAutoSyncMaster = businessPreferences?.enable_auto_sync_master?.value;
  const downloadUrl = `${autoConfigUrls.downloadStockItems}?business_id=${globSelectedBusiness?.business_id}`;
  const uploadUrl = `${autoConfigUrls.uploadStockItems}?business_id=${globSelectedBusiness?.business_id}`;

  return (
    <DataTable
      title="List of Items"
      url={`${autoConfigUrls.getStockItems}?business_id=${globSelectedBusiness?.business_id}&is_show_aliases=true`}
      columns={COLUMNS}
    >
      {!isAutoSyncMaster && (
        <>
          <DownloadBtn
            className="!rounded-md"
            selectionOptions={[
              { label: 'Items List', value: 'items-list', url: downloadUrl },
              { label: 'Items Aliases', value: 'items-aliases', url: downloadUrl },
            ]}
          />
          <UploadBtn
            className="!rounded-md"
            selectionOptions={[
              { label: 'Items List', value: 'items-list', url: uploadUrl },
              { label: 'Items Aliases', value: 'items-aliases', url: uploadUrl },
            ]}
            selectionLabel="What to upload?"
          />
        </>
      )}
      {isZoho && isAutoSyncMaster && (
        <>
          <Button variant="contained" className="!rounded-md" onClick={() => setOpen(true)}>
            Add Item
          </Button>
          <AddItemForm open={open} onClose={() => setOpen(false)} />
        </>
      )}
    </DataTable>
  );
}

export default Item;
