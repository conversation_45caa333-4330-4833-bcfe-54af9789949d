import React from 'react';
import { FormControlLabel } from '@mui/material';

function DynamicField({
  Component = null,
  config = null,
  label = null,
  required = null,
  disabled = null,
  control = null,
  size = 'small',
  ...props
}) {
  if (!Component && !control) throw new Error('Either Component or control is required');
  if (config && !config?.show_field) return null;
  const isDisabled = disabled !== null ? disabled : config?.access !== 'RW';
  const isRequired = required !== null ? required : !!config?.mandatory;

  return (
    <div className="flex flex-col gap-1">
      {label && !control && (
        <label className={`text-sm font-medium ${isDisabled ? 'text-gray-400' : 'text-gray-700'}`}>
          {label}
          {isRequired && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {control ? (
        <FormControlLabel control={React.cloneElement(control, { disabled: isDisabled, ...props })} label={label} />
      ) : (
        <Component fullWidth size={size} required={isRequired} disabled={isDisabled} {...props} />
      )}
    </div>
  );
}

export default DynamicField;
