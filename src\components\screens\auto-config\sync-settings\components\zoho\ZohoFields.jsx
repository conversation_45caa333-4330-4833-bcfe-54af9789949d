import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Switch, Button, IconButton } from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { ChevronDown } from 'lucide-react';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../../utils/apiUtils';
import { useAuth } from '../../../../../../contexts/AuthContext';
import { updateBusinessPreferences } from '../../../../../services/syncSettingsServices';
import { useOutletContext } from 'react-router-dom';
import DynamicField from '../../../../../custom-components/DynamicField';
import ZohoAuthBtn from './ZohoAuthBtn';

const regions = ['in'];

const syncTimes = [
  '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
  '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
  '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
  '18:00', '19:00', '20:00', '21:00', '22:00', '23:00',
];

const getSettingsJsonKey = (key) => {
  switch (key) {
    case 'autoSyncMasters':
      return 'enable_auto_sync_master';
    case 'autoSyncValidation':
      return 'enable_auto_sync_invoice';
    case 'dailySyncTime':
      return 'sync_time';
    default:
      return key;
  }
};

function ZohoFields({ setIsConnected, isConnected, zohoConfigs }) {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences, refetch } = useOutletContext();
  
  // Authentication state
  const [zohoRegion, setZohoRegion] = useState(zohoConfigs?.region || 'in');
  const [organizationId, setOrganizationId] = useState(zohoConfigs?.organization_id || '');
  const [copied, setCopied] = useState(false);
  
  // Sync settings state
  const [MISettings, setMISettings] = useState({
    autoSyncMasters: businessPreferences?.enable_auto_sync_master?.value,
    autoSyncValidation: businessPreferences?.enable_auto_sync_invoice?.value,
    dailySyncTime: businessPreferences?.preferred_sync_time?.value,
  });

  const handleChangeMISettings = (e, key) => {
    const jsonKey = getSettingsJsonKey(key);
    const value = key === 'dailySyncTime' ? e.target.value : e.target.checked;
    updateBusinessPreferences(globSelectedBusiness?.business_id, {
      [jsonKey]: value,
    })
      .then(() => {
        setMISettings((prev) => ({
          ...prev,
          [key]: value,
        }));
        refetch();
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        toast.error(`Failed to update settings: ${errorMessage}`);
      });
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(organizationId);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  const handleGetHelp = () => {
    // Open support or documentation link
    window.open('https://help.zoho.com/portal/en/kb/books', '_blank');
  };

  return (
    <div className="flex justify-evenly gap-6">
      <div className="flex justify-between min-w-[60%] gap-16 p-5 rounded-2xl shadow-lg bg-white border border-accent1-border">
        <div className="flex flex-col gap-4">
          <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
            Synchronization Settings
          </Typography>
          <div className="flex flex-col gap-4">
            <DynamicField
              config={businessPreferences?.enable_auto_sync_master}
              control={
                <Switch 
                  checked={MISettings.autoSyncMasters} 
                  onChange={(e) => handleChangeMISettings(e, 'autoSyncMasters')} 
                  color="primary" 
                  disabled={!isConnected}
                />
              }
              label="Auto Sync Master"
              disabled={!isConnected}
            />

            <DynamicField
              control={
                <Switch
                  checked={MISettings.autoSyncValidation}
                  onChange={(e) => handleChangeMISettings(e, 'autoSyncValidation')}
                  color="primary"
                  disabled={!isConnected}
                />
              }
              config={businessPreferences?.enable_auto_sync_invoice}
              label="Auto Sync On Validation"
              disabled={!isConnected}
            />

            <div className="flex flex-col gap-2">
              <Typography variant="body1" className="text-gray-600 text-base">
                Daily Sync Time
              </Typography>
              <div className="relative w-64 max-w-[200px]">
                <select
                  value={MISettings.dailySyncTime || ''}
                  onChange={(e) => handleChangeMISettings(e, 'dailySyncTime')}
                  disabled={!isConnected}
                  className={`w-full text-lg font-medium px-3 py-2 pr-12 rounded-xl border-2 focus:outline-none appearance-none ${
                    !isConnected
                      ? 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                      : 'hover:shadow-md text-gray-900 bg-accent1-bg border-accent1-border'
                  }`}
                >
                  <option value="">Select Time</option>
                  {syncTimes.map((time) => (
                    <option key={time} value={time}>
                      {time}
                    </option>
                  ))}
                </select>
                <ChevronDown
                  className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${
                    !isConnected ? 'text-gray-400' : 'text-accent1'
                  }`}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4 flex-1">
          <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
            API Configuration
          </Typography>
          
          <div className="flex flex-col gap-4">
            <div className="flex flex-col">
              <p className="text-gray-600 text-base m-0">Organization ID</p>
              <div className="flex items-center gap-2 rounded-md p-2">
                <input
                  type="text"
                  value={organizationId}
                  onChange={(e) => setOrganizationId(e.target.value)}
                  className="h-10 flex-grow p-2 border rounded-md font-mono text-gray-600 bg-accent1-bg border-accent1-border"
                  placeholder="Enter your Organization ID"
                />
                <IconButton disabled={!organizationId} onClick={handleCopy}>
                  {copied ? <CheckIcon color="success" /> : <ContentCopyIcon />}
                </IconButton>
              </div>
            </div>

            <div className="flex flex-col">
              <p className="text-gray-600 text-base m-0">Zoho Region</p>
              <div className="relative">
                <select
                  value={zohoRegion}
                  onChange={(e) => {
                    setZohoRegion(e.target.value);
                    setIsConnected(false);
                    setOrganizationId('');
                  }}
                  className="w-full text-lg font-medium px-3 py-2 pr-8 rounded-xl border-2 focus:outline-none appearance-none hover:shadow-md text-gray-900 bg-accent1-bg border-accent1-border"
                >
                  {regions.map((region) => (
                    <option key={region} value={region}>
                      .{region}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none text-accent1" />
              </div>
            </div>
          </div>

          <ZohoAuthBtn
            setIsConnected={setIsConnected}
            isConnected={isConnected}
            organizationId={organizationId}
            region={zohoRegion}
          />
        </div>
      </div>

      <div className="flex flex-col justify-between gap-2 p-5 rounded-2xl shadow-lg bg-white border border-accent1-border">
        <Typography variant="h6" className="font-semibold mb-2 text-gray-800 border-b pb-2">
          Zoho Setup Instructions
        </Typography>

        <div className="flex flex-col justify-between gap-2">
          <p className="text-gray-600 text-lg">
            To set up Zoho integration, enter your Organization ID and select your region, then connect to Zoho Books.
            If you need assistance with finding your Organization ID or setting up the integration, please contact our support team.
          </p>

          <Button
            id="get-help-btn"
            onClick={handleGetHelp}
            variant="contained"
            startIcon={<HelpOutlineIcon size={16} />}
          >
            Get Help
          </Button>
        </div>
      </div>
    </div>
  );
}

export default ZohoFields;
