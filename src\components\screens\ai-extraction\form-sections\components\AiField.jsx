import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Tooltip } from 'react-tooltip';
import { Copy, Check, CornerRightUp } from 'lucide-react';
import { ErrorIcon, SuccessIcon, WarningAlertIcon } from '../../../../../assets/svgs';

const getStatusIcon = (status) => {
  switch (status) {
    case 'error':
      return <ErrorIcon data-testid="errorIcon" id="errorIcon" className="w-6 h-6" />;
    case 'warning':
      return <WarningAlertIcon data-testid="warningIcon" id="warningIcon" className="w-6 h-6" />;
    case 'exactMatch':
      return <SuccessIcon data-testid="successIcon" id="successIcon" className="w-6 h-6" />;
    default:
      return '';
  }
};

const getStatusTextColor = (status) => {
  switch (status) {
    case 'error':
      return 'text-red-500';
    case 'warning':
      return 'text-amber-500';
    case 'exactMatch':
      return 'text-green-600';
    default:
      return '';
  }
};

const getStatus = (alertObject) => {
  if (alertObject?.error) return 'error';
  if (alertObject?.warning) return 'warning';
  return null;
};

const AiField = ({
  label,
  alertObject = null,
  required = false,
  isExactMatch = false,
  className = '',
  copyText = '',
  onCopied = null,
  renderBelowField = null,
  onPaste = null,
  inputClass,
  Element = 'input',
  elementClass = '',
  renderCustomField = null,
  alignSuggestionRight = false,
  onRequiredChange = null,
  id = crypto.randomUUID(),
  ...props
}) => {
  const [isCopied, setIsCopied] = useState(false);

  const { status, statusMsg, longStatusMsg, statusIcon, statusTextColor } = useMemo(() => {
    const status = isExactMatch ? 'exactMatch' : getStatus(alertObject);
    return {
      status,
      statusMsg: alertObject?.error || alertObject?.warning || null,
      longStatusMsg: alertObject?.longStatusMsg || null,
      statusIcon: getStatusIcon(isExactMatch ? 'exactMatch' : status),
      statusTextColor: getStatusTextColor(isExactMatch ? 'exactMatch' : status),
    };
  }, [alertObject, isExactMatch]);

  const handleCopy = useCallback(() => {
    navigator.clipboard
      .writeText(copyText)
      .then(() => {
        setIsCopied(true);
        onCopied && onCopied(copyText);

        setTimeout(() => {
          setIsCopied(false);
        }, 500);
      })
      .catch((error) => {
        console.error('Clipboard write failed:', error);
      });
  }, [copyText, onCopied]);

  const handlePaste = useCallback(() => {
    if (props?.disabled) return;
    const fieldName = props?.name;
    onPaste && onPaste(copyText, fieldName);
  }, [copyText, props?.disabled, props?.name]);

  useEffect(() => {
    onRequiredChange && onRequiredChange(required, props?.name);
  }, [required]);

  return (
    <>
      <div className={className}>
        <div className="flex flex-col items-start gap-1 w-full mb-2">
          <div className="flex flex-row items-center gap-0.5 ml-1 text-[13px] text-nowrap leading-tight">
            <label htmlFor={id}>{label}</label>
            {required && <span className="text-[#E23131]">*</span>}
          </div>
          <div className="w-full">
            <div id="extraction-field-alert" className="flex flex-col gap-1">
              <div className="relative">
                {/* Render given prop field otherwise typical input field with type */}
                {renderCustomField ? (
                  renderCustomField()
                ) : (
                  <Element className={`input-field ${elementClass}`} required id={id} {...props} />
                )}

                {/* Error/Warning/Found icon inside input field */}
                {status && (
                  <div
                    id="extraction-alert"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  >
                    <div id={`tooltip-${id}`} data-testid={`tooltip-${id}`} className="flex items-center py-2">
                      {getStatusIcon(status)}
                    </div>
                  </div>
                )}
              </div>
              {renderBelowField && renderBelowField}
              {/* Suggestion */}
              {(copyText || copyText === 0) && (
                <div className={`flex ${alignSuggestionRight ? 'justify-end' : ''} gap-2 text-sm mt-0.5 ml-2`}>
                  {copyText}{' '}
                  <div className="flex items-center gap-2 md:gap-1">
                    {isCopied ? (
                      <Check className="w-3.5 h-3.5 flex-shrink-0 text-green-600" />
                    ) : (
                      <Copy
                        className="w-3.5 h-3.5 flex-shrink-0 cursor-pointer"
                        onClick={handleCopy}
                        data-testid={`copy-icon-${id}`}
                      />
                    )}
                    <CornerRightUp
                      className={`w-3.5 h-3.5 flex-shrink-0 ${props?.disabled ? 'opacity-50' : 'cursor-pointer'}`}
                      onClick={handlePaste}
                      data-testid={`paste-icon-${id}`}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {!isExactMatch && (
        <ShowTooltip
          status={status}
          statusMsg={statusMsg}
          longStatusMsg={longStatusMsg}
          statusIcon={statusIcon}
          statusTextColor={statusTextColor}
          id={id}
        />
      )}
    </>
  );
};

const ShowTooltip = React.memo(({ status, statusMsg, longStatusMsg, statusIcon, statusTextColor, id }) => {
  if (!statusMsg && status === null) return <></>;
  return (
    <Tooltip
      anchorSelect={`#tooltip-${id}`}
      place="bottom"
      data-tooltip-offset={10}
      style={{
        background: '#181D27',
        borderRadius: '10px',
        maxWidth: '300px',
        padding: 0,
        margin: 0,
        zIndex: 1000,
        textAlign: 'left',
      }}
    >
      <div className="rounded-lg p-4 w-full h-full">
        <div className="flex items-center space-x-2">
          {statusIcon}
          <p className={`font-medium text-lg ${statusTextColor}`}>{statusMsg || 'No error found'}</p>
        </div>

        <div className="pt-1">
          <p className="text-white text-base h-full">{longStatusMsg}</p>
        </div>
      </div>
    </Tooltip>
  );
});
export default AiField;
