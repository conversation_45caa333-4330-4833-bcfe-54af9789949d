import React from 'react';
import AutoConfigDropdown from '../AutoConfigDropdown';
import { INDIAN_STATES } from '../../../utils/constants';
import { Button, TextField, Typography } from '@mui/material';
import { Form, Formik } from 'formik';
import DynamicField from '../../../custom-components/DynamicField';

function General() {
  const handleSubmit = (values) => {
    console.log('values ::: ', values);
  };

  const initialValues = {
    business_name: 'abc-123',
    gst_status: '',
    gst_no: '',
    address: '',
    state: '',
  };

  return (
    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
      {({ values, setFieldValue, dirty }) => (
        <Form className="flex flex-col gap-4 px-5 max-w-4xl ml-10">
          <Typography variant="h6" fontWeight="bold" className="mb-5">
            Business Information
          </Typography>

          <DynamicField
            Component={TextField}
            label="Business Name"
            value={values.business_name}
            onChange={(e) => setFieldValue('business_name', e.target.value)}
            placeholder="Enter Business Name"
          />

          <DynamicField
            Component={TextField}
            label="GST NO."
            value={values.gst_no}
            onChange={(e) => setFieldValue('gst_no', e.target.value)}
            fullWidth
            size="small"
            placeholder="Enter GST number"
          />

          <DynamicField
            Component={TextField}
            label="Address"
            onSelect={(value) => setFieldValue('address', value?.uuid_id ?? '')}
            value={values.address}
            placeholder="Enter Address"
            multiline
            minRows={3}
            maxRows={5}
            className="resize-none border border-gray-50"
          />

          <AutoConfigDropdown
            label="State"
            options={INDIAN_STATES}
            onSelect={(value) => setFieldValue('state', value?.code_num ?? '')}
            value={values.state}
            optionLabel="state_name_code_num"
          />

          <AutoConfigDropdown
            label="GST Status"
            onSelect={(value) => setFieldValue('gst_status', value?.uuid_id ?? '')}
            value={values.gst_status}
          />

          <Button variant="contained" className="w-fit" type="submit" disabled={!dirty}>
            Save
          </Button>
        </Form>
      )}
    </Formik>
  );
}

export default General;
